name: Publish to PyPI

on:
  release:
    types: [published]
  
  # Allows manual trigger from GitHub Actions tab
  workflow_dispatch:

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'
      
      - name: Install Poetry
        run: |
          curl -sSL https://install.python-poetry.org | python3 -
      
      - name: Configure Poetry
        run: |
          poetry config pypi-token.pypi ${{ secrets.PYPI_TOKEN }}
      
      - name: Build package
        run: poetry build
      
      - name: Publish to PyPI
        run: poetry publish