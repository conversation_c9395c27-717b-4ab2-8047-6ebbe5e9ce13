from daytona_sdk import Daytona, DaytonaConfig
  
# Define the configuration
config = DaytonaConfig(api_key="dtn_9adf14a05b4115c40ebbd322279251a9a4a88f59e1aae8e264034567ae640dd1")

# Initialize the Daytona client
daytona = Daytona(config)

# Create the Sandbox instance
sandbox = daytona.create()

# Run the code securely inside the Sandbox
response = sandbox.process.code_run('print("Hello World from code!")')
if response.exit_code != 0:
  print(f"Error: {response.exit_code} {response.result}")
else:
    print(response.result)