'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { createClient } from '@/lib/supabase/client';
import { User, Session } from '@supabase/supabase-js';
import { SupabaseClient } from '@supabase/supabase-js';

type AuthContextType = {
  supabase: SupabaseClient;
  session: Session | null;
  user: User | null;
  isLoading: boolean;
  signOut: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const supabase = createClient();
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check if we're in development mode
  const isDevelopment = process.env.NEXT_PUBLIC_ENV_MODE === 'LOCAL';

  useEffect(() => {
    const getInitialSession = async () => {
      // In development mode, provide a mock user to bypass authentication
      if (isDevelopment) {
        const mockUser = {
          id: 'dev-user-123',
          email: '<EMAIL>',
          user_metadata: {
            full_name: 'Development User',
            avatar_url: null,
          },
          app_metadata: {},
          aud: 'authenticated',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          email_confirmed_at: new Date().toISOString(),
          phone_confirmed_at: null,
          confirmation_sent_at: null,
          recovery_sent_at: null,
          email_change_sent_at: null,
          new_email: null,
          invited_at: null,
          action_link: null,
          phone: null,
          role: 'authenticated',
          last_sign_in_at: new Date().toISOString(),
          identities: [],
          factors: [],
        } as User;

        const mockSession = {
          access_token: 'dev-access-token',
          refresh_token: 'dev-refresh-token',
          expires_in: 3600,
          expires_at: Math.floor(Date.now() / 1000) + 3600,
          token_type: 'bearer',
          user: mockUser,
        } as Session;

        setSession(mockSession);
        setUser(mockUser);
        setIsLoading(false);
        return;
      }

      // Normal authentication flow for production
      const {
        data: { session: currentSession },
      } = await supabase.auth.getSession();
      setSession(currentSession);
      setUser(currentSession?.user ?? null);
      setIsLoading(false);
    };

    getInitialSession();

    // Only set up auth listener in production
    if (!isDevelopment) {
      const { data: authListener } = supabase.auth.onAuthStateChange(
        (_event, newSession) => {
          setSession(newSession);
          setUser(newSession?.user ?? null);
          // No need to set loading state here as initial load is done
          // and subsequent changes shouldn't show a loading state for the whole app
          if (isLoading) setIsLoading(false);
        },
      );

      return () => {
        authListener?.subscription.unsubscribe();
      };
    }
  }, [supabase, isLoading, isDevelopment]); // Added isDevelopment to dependencies

  const signOut = async () => {
    if (isDevelopment) {
      // In development mode, just clear the mock user
      setSession(null);
      setUser(null);
      return;
    }

    await supabase.auth.signOut();
    // State updates will be handled by onAuthStateChange
  };

  const value = {
    supabase,
    session,
    user,
    isLoading,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
