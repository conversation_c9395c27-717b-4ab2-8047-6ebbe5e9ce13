# Environment Mode
# Valid values: local, staging, production
ENV_MODE=local

#DATABASE
SUPABASE_URL=https://jmmmzhnqkgvibxrhpncy.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.ZfSzW-GkoMEL4h47GyCg7F0bfYx1cpOty-Kp8SWBUW4
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.qzPtmUOPsrN2nu7fVTRn-VnwTsB4MRZ0mVmPpxSoBfM

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_SSL=false

RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672

# LLM Providers:
ANTHROPIC_API_KEY=
OPENAI_API_KEY=
MODEL_TO_USE=

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION_NAME=

GROQ_API_KEY=
OPENROUTER_API_KEY=

# DATA APIS
RAPID_API_KEY=

# WEB SEARCH
TAVILY_API_KEY=

# WEB SCRAPE
FIRECRAWL_API_KEY=
FIRECRAWL_URL=

# Sandbox container provider:
DAYTONA_API_KEY=dtn_9adf14a05b4115c40ebbd322279251a9a4a88f59e1aae8e264034567ae640dd1
DAYTONA_SERVER_URL=https://sandbox.daytona.ai
DAYTONA_TARGET=dd46fa39-867f-4953-b6ac-d84bdad7449a
