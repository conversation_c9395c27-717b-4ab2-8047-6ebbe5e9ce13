#!/usr/bin/env python3
import os
import sys
import time
import platform
import subprocess
from getpass import getpass
import re
from backend.utils.config import Configuration


# ANSI colors for pretty output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'


def print_banner():
    """Print Suna setup banner."""
    print(f"""
{Colors.BLUE}{Colors.BOLD}
   ███████╗██╗   ██╗███╗   ██╗ █████╗ 
   ██╔════╝██║   ██║████╗  ██║██╔══██╗
   ███████╗██║   ██║██╔██╗ ██║███████║
   ╚════██║██║   ██║██║╚██╗██║██╔══██║
   ███████║╚██████╔╝██║ ╚████║██║  ██║
   ╚══════╝ ╚═════╝ ╚═╝  ╚═══╝╚═╝  ╚═╝
                                      
   Setup Wizard
{Colors.ENDC}
""")


def run_command(command, cwd=None):
    """Utility to run shell commands with error handling."""
    try:
        result = subprocess.run(
            command,
            cwd=cwd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print_error(f"Command failed: {' '.join(command)}")
        print_error(f"Error: {e.stderr.strip()}")
        sys.exit(1)


def validate_env_vars(env_vars):
    """Validate essential environment variables."""
    required_keys = ['SUPABASE_URL', 'SUPABASE_ANON_KEY', 'DAYTONA_API_KEY']
    for key in required_keys:
        if not env_vars.get(key):
            print_error(f"Missing required environment variable: {key}")
            sys.exit(1)


def configure_env_file(target_path, config):
    """Write environment variables to a file."""
    with open(target_path, 'w') as env_file:
        for key, value in config.items():
            env_file.write(f"{key}={value}\n")
    print_success(f"Configuration written to {target_path}")


def install_package(package_name):
    """Install a Python package using pip."""
    try:
        print_info(f"Installing {package_name}...")
        run_command([sys.executable, '-m', 'pip', 'install', package_name])
        print_success(f"{package_name} installed successfully.")
    except Exception as e:
        print_error(f"Failed to install {package_name}: {e}")
        sys.exit(1)


def check_tool_installed(command, install_url):
    """Check if a command-line tool is installed."""
    try:
        run_command([command, '--version'])
        print_success(f"{command} is installed.")
    except Exception:
        print_error(f"{command} is not installed.")
        print_info(f"Visit {install_url} to download and install {command}.")
        sys.exit(1)


def print_step(step_num, total_steps, step_name):
    """Print a step header."""
    print(f"\n{Colors.BLUE}{Colors.BOLD}Step {step_num}/{total_steps}: {step_name}{Colors.ENDC}")
    print(f"{Colors.CYAN}{'='*50}{Colors.ENDC}\n")


def print_info(message):
    """Print an informational message."""
    print(f"{Colors.CYAN}ℹ️  {message}{Colors.ENDC}")


def print_success(message):
    """Print a success message."""
    print(f"{Colors.GREEN}✅  {message}{Colors.ENDC}")


def print_error(message):
    """Print an error message."""
    print(f"{Colors.RED}❌  {message}{Colors.ENDC}")


def print_warning(message):
    """Print a warning message."""
    print(f"{Colors.YELLOW}⚠️  {message}{Colors.ENDC}")


def main():
    total_steps = 6
    current_step = 1

    # Print banner
    print_banner()
    print("Welcome to the Suna setup wizard.\n")

    # Step 1: Check requirements
    print_step(current_step, total_steps, "Checking system requirements")
    requirements = {
        'git': 'https://git-scm.com/downloads',
        'docker': 'https://docs.docker.com/get-docker/',
        'python3': 'https://www.python.org/downloads/',
        'poetry': 'https://python-poetry.org/docs/#installation',
        'node': 'https://nodejs.org/en/download/',
        'npm': 'https://docs.npmjs.com/downloading-and-installing-node-js-and-npm'
    }
    for tool, url in requirements.items():
        check_tool_installed(tool, url)
    current_step += 1

    # Step 2: Collect Supabase info
    print_step(current_step, total_steps, "Collecting Supabase information")
    supabase_info = {
        'SUPABASE_URL': input("Enter your Supabase Project URL: "),
        'SUPABASE_ANON_KEY': input("Enter your Supabase Anon Key: "),
    }
    validate_env_vars(supabase_info)
    current_step += 1

    # Step 3: Configure environment files
    print_step(current_step, total_steps, "Configuring environment files")
    configure_env_file('.env', supabase_info)
    current_step += 1

    # Step 4: Install dependencies
    print_step(current_step, total_steps, "Installing dependencies")
    install_package('poetry')
    current_step += 1

    # Step 5: Start Suna
    print_step(current_step, total_steps, "Starting Suna")
    print_info("Use 'docker compose up -d' to start Suna.")
    print_success("Suna setup is complete! Access it at http://localhost:3000.")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nSetup interrupted. Run the script again to resume.")
        sys.exit(1)